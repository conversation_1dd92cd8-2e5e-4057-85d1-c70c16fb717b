package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.PayTuitionHistoryBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.PayTuitionHistoryContract;
import com.dep.biguo.mvp.ui.adapter.PayTuitionHistoryAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class PayTuitionHistoryPresenter extends BasePresenter<PayTuitionHistoryContract.Model,PayTuitionHistoryContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    PayTuitionHistoryAdapter mAdapter;

    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    private int mPage = 1;

    private PayResultListener mPayListener;

    @Inject
    public PayTuitionHistoryPresenter(PayTuitionHistoryContract.Model model, PayTuitionHistoryContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        mPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void initRefresh(){
        getHistoryList(INIT,1);
    }

    public void refresh(){
        getHistoryList(REFRESH,1);
    }

    public void loadMore(){
        getHistoryList(LOAD,mPage+1);
    }

    public void getHistoryList(int action, int page){
        mModel.getHistoryList(page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (action == INIT)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<PayTuitionHistoryBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<PayTuitionHistoryBean>> response) {
                        if (response.isSuccess()) {
                            if (action == INIT || action == REFRESH) {
                                mAdapter.setNewData(response.getData());
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(response.getData())) {
                                    mRootView.showEmptyView();
                                }else {
                                    mRootView.hideLoading();
                                }
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                mAdapter.addData(response.getData());
                                mAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if (AppUtil.isEmpty(response.getData()))
                                mAdapter.loadMoreEnd();
                        }else {
                            mRootView.finishRefresh();
                            mAdapter.loadMoreFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.finishRefresh();
                        mAdapter.loadMoreFail();
                        if (AppUtil.isEmpty(mAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                });
    }

    public void getSignUrl(int protocol_id){
        mModel.getSignUrl(protocol_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Map<String, String>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Map<String, String>> response) {
                        if (response.isSuccess()) {
                            Map<String, String> map = response.getData();
                            String url = map.get("url");//永久有效的签署链接
                            mRootView.getSignUrlSuccess(url);
                        }
                    }
                });
    }

    public void getLookSignUrl(int protocol_id){
        mModel.getLookSignUrl(protocol_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Map<String, String>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Map<String, String>> response) {
                        if (response.isSuccess()) {
                            Map<String, String> map = response.getData();
                            String url = map.get("url");//永久有效的签署链接
                            mRootView.getSignUrlSuccess(url);
                        }
                    }
                });
    }

    public void cancelPay(int id){
        mModel.cancelPay(id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Object> response) {
                        if (response.isSuccess()) {
                            //取消成功，本地更改历史记录
                            mRootView.payStatusChange(null);
                        } else {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }
                });
    }

    public void toPay(String payType, int order_id) {
        mModel.singlePay(payType, order_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Object> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().toString());
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }
    public void toTuition(int pay_record_id) {
        mModel.toTuition(pay_record_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<String>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<String> s) {
                        if (s.isSuccess()) {
                            mRootView.createQRCode(s.getData());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
