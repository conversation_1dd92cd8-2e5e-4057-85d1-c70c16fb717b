package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationQualificationsBean;
import com.dep.biguo.mvp.contract.OrganizationQualificationsContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationQualificationsPresenter extends BasePresenter<OrganizationQualificationsContract.Model, OrganizationQualificationsContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationQualificationsPresenter(OrganizationQualificationsContract.Model model, OrganizationQualificationsContract.View rootView) {
        super(model, rootView);
    }

    public void getQualifications(int institution_id) {
        mModel.getQualifications(institution_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationQualificationsBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationQualificationsBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.getQualificationsSuccess(s.getData());
                            }
                        }else {
                            mRootView.showEmptyView();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });

    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
