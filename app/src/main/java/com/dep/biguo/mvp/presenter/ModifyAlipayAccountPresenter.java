package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.CountDownTimer;
import android.text.TextUtils;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VerifyCodeBean;
import com.dep.biguo.mvp.contract.ModifyAlipayAccountContract;
import com.dep.biguo.utils.RSAEncrypt;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


@ActivityScope
public class ModifyAlipayAccountPresenter extends BasePresenter<ModifyAlipayAccountContract.Model, ModifyAlipayAccountContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private CountDownTimer mCountDownTimer;

    @Inject
    public ModifyAlipayAccountPresenter(ModifyAlipayAccountContract.Model model, ModifyAlipayAccountContract.View rootView) {
        super(model, rootView);
        mCountDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if(mRootView == null) return;
                mRootView.setVerifyTimerText(String.format("%s秒", (int) millisUntilFinished / 1000), false);
            }

            @Override
            public void onFinish() {
                mRootView.setVerifyTimerText("重新发送", true);
            }
        };
    }


    /**获取图形验证码
     * @param phone 手机号
     */
    public void getImageVerifyCode(String phone) {
        try {
            mModel.getImageVerifyCode(phone)
                    .subscribeOn(Schedulers.io())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse<VerifyCodeBean>>(mErrorHandler) {
                        @Override
                        public void onNext(BaseResponse<VerifyCodeBean> userBeanBaseResponse) {
                            if (userBeanBaseResponse.isSuccess()) {
                                if(!TextUtils.isEmpty(userBeanBaseResponse.getData().getImg_url())){
                                    mRootView.getImageVerifyCodeSuccess(userBeanBaseResponse.getData().getImg_url());
                                    return;
                                }
                            }
                            mRootView.showMessage("获取图片验证码失败");
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**获取短信验证码
     * @param phone 手机号
     * @param captcha 图形验证码
     */
    public void getSmsVerifyCode(String phone, String captcha, int type) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("mobile", phone);
            params.put("captcha", captcha);
            //type取值有4、5、6、7、8
            params.put("type", type);//1验证码登录，2手机号修改密码，3重置密码，4微信登录绑定手机号，5更换手机号验证账号，6更换手机号-更新手机号，7更换支付宝账号验证账号，8注销账号
            mModel.getSmsVerifyCode(RSAEncrypt.encryptByPublicKey(GsonUtils.toJson(params)))
                    .subscribeOn(Schedulers.io())
                    .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .doFinally(() -> mRootView.hideLoadingDialog())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                        @Override
                        public void onNext(BaseResponse userBeanBaseResponse) {
                            if (userBeanBaseResponse.isSuccess()) {
                                mCountDownTimer.start();
                                mRootView.showMessage(userBeanBaseResponse.getResult_info());
                            }
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**绑定支付宝
     * */
    public void bindZfb(int users_id, String zfb, String name, String mobile, String verifycode) {
        mModel.bindZfb(users_id, zfb, name, mobile, verifycode)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.bindZfbSuccess();
                        } else {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        mCountDownTimer.cancel();
        mCountDownTimer = null;
    }
}
