package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationStudyGoodsBean;
import com.dep.biguo.mvp.contract.OrganizationStudyGoodsContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationStudyGoodsPresenter extends BasePresenter<OrganizationStudyGoodsContract.Model, OrganizationStudyGoodsContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationStudyGoodsPresenter(OrganizationStudyGoodsContract.Model model, OrganizationStudyGoodsContract.View rootView) {
        super(model, rootView);
    }

    public void getOrganizationStudyGoodsData(int product_id){
        mModel.getOrganizationStudyGoodsData(product_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationStudyGoodsBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<OrganizationStudyGoodsBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getOrganizationStudyGoodsDataSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
