package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.bean.InternetStudyGoodsBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.InternetStudyGoodsContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.AESEncrypt;
import com.dep.biguo.utils.CipherUtil;
import com.dep.biguo.utils.RSAEncrypt;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class InternetStudyGoodsPresenter extends BasePresenter<InternetStudyGoodsContract.Model, InternetStudyGoodsContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private PayResultListener mResultPayListener;//支付的监听接口
    private List<DiscountBean> discountList;//优惠券列表
    private InternetStudyGoodsBean internetStudyGoodsBean;

    private int order_id;
    private int exam_info_status;// 0=无报考信息， 1=可修改报考信息， 2 =不可修改报考信息

    public InternetStudyGoodsBean getInternetStudyGoodsBean() {
        return internetStudyGoodsBean;
    }

    @Inject
    public InternetStudyGoodsPresenter(InternetStudyGoodsContract.Model model, InternetStudyGoodsContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess(order_id, exam_info_status);
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void getInternetStudyGoodsData(){
        mModel.getInternetStudyGoodsData(PayUtils.INTERNET_STUDY)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<InternetStudyGoodsBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<InternetStudyGoodsBean> response) {
                        if (response.isSuccess()) {
                            if(response.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                internetStudyGoodsBean = response.getData();
                                mRootView.getInternetStudyGoodsDataSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(new Throwable());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getInternetStudyCourseData(){
        mModel.getInternetStudyCourseData(PayUtils.INTERNET_STUDY)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<InternetStudyGoodsBean.Course>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<InternetStudyGoodsBean.Course>> response) {
                        if (response.isSuccess()) {
                            mRootView.getInternetStudyCourseDataSuccess(response.getData());
                        }
                    }
                });
    }

    /**获取优惠券
     *
     */
    public void getDiscountCard(InternetStudyGoodsBean.Course course, int is_bundle, String totalPrice){
        if(discountList != null) {
            mRootView.showPayDialog(discountList, totalPrice, course, is_bundle);
            return;
        }

        mModel.getDiscountCard(0, PayUtils.INTERNET_STUDY, "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(discountList, totalPrice, course, is_bundle);
                        }
                    }

                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(new ArrayList<>(), totalPrice, course, is_bundle);
                    }
                });
    }

    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void payOrder(InternetStudyGoodsBean.Course course, int is_bundle, String payType, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(course.getCode(), is_bundle, payType, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        mModel.payOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            //保存一下订单号，方便待会填写报名信息
                            order_id = s.getData().getOrder_id();
                            exam_info_status = s.getData().getExam_info_status();

                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }

                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mRootView.paySuccess(order_id, exam_info_status);
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }
                        }else {
                            //被抢拼的团将会被锁定45秒，45秒内不能使用拼团ID创建新的订单
                            mRootView.showMessage(s.getResult_info());
                        }
                    }
                });
    }

    private PayParamsBean getParams(String code, int is_bundle, String payType, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.CODE, code);
        paramsBean.put(PayParamsBean.TYPE, PayUtils.INTERNET_STUDY);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.IS_BUNDLE, is_bundle);
        if(selectedDiscountBean != null) {
            paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean.getId());
        }
        return paramsBean;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
