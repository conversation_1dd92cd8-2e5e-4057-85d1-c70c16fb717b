package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationReservationBean;
import com.dep.biguo.mvp.contract.OrganizationReservationContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class OrganizationReservationPresenter extends BasePresenter<OrganizationReservationContract.Model, OrganizationReservationContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationReservationPresenter(OrganizationReservationContract.Model model, OrganizationReservationContract.View rootView) {
        super(model, rootView);
    }

    public void getReservationRoom(int institution_id) {
        mModel.getReservationRoom(institution_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationReservationBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationReservationBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getReservationRoomSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });

    }

    public void getReservationCourse(int id, int type, int page) {
        mModel.getReservation(id, type, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationReservationBean.Reservation>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationReservationBean.Reservation>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getReservationCourseSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getReservationCourseSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getReservationCourseFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getReservationCourseFail();
                    }
                });

    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
