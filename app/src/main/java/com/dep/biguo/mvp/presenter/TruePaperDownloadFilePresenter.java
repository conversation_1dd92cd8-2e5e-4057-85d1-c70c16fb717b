package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.TruePaperDownloadFileBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.TruePaperDownloadFileContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.mvp.ui.activity.TruePaperDownloadFileActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class TruePaperDownloadFilePresenter extends BasePresenter<TruePaperDownloadFileContract.Model, TruePaperDownloadFileContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private PayResultListener mResultPayListener;//支付的监听接口

    @Inject
    public TruePaperDownloadFilePresenter(TruePaperDownloadFileContract.Model model, TruePaperDownloadFileContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void getTruePaperFileList(int courseId, int page) {
        mModel.getTruePaperFileList(courseId+"", page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<TruePaperDownloadFileBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<TruePaperDownloadFileBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getTruePaperFileListSuccess(new ArrayList<>());

                            }else {
                                mRootView.showSuccessView();
                                mRootView.getTruePaperFileListSuccess(s.getData());
                            }
                        }else {
                            mRootView.getTruePaperFileListFail();
                            mRootView.showErrorView(new Throwable());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.getTruePaperFileListFail();
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     * @param
     */
    public void payOrder(String payType, String code, String[] paperIds) {
        mModel.paySingleOrder(getCreateOrderParams(payType, code, paperIds))
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }

    public void shareUnlock(int courseId, int exams_real_paper_id, int type) {
        mModel.shareUnlock(exams_real_paper_id, type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse result) {
                        if (result.isSuccess()) {
                            mRootView.shareUnlockSuccess();
                        }
                    }
                });
    }

    public long computerFileSize(String courseName){
        File file = new File(String.format("%s/%s", TruePaperDownloadFileActivity.getDirPath(mRootView.getActivity()), courseName));
        if(file.exists()){
            if(file.isDirectory()){
                long size = 0;
                File[] allChildFile = file.listFiles();
                if(allChildFile != null) {
                    for (File childFile : allChildFile) {
                        size += childFile.length();
                    }
                }
                return size;
            }else {
                return file.length();
            }
        }
        return 0;
    }

    /**构建创建订单需要的参数
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     * @return
     */
    private Map<String,Object> getCreateOrderParams(String payType, String code, String[] paperIds){
        StringBuilder paperStr = new StringBuilder();
        for(String paperId : paperIds){
            paperStr.append(paperId).append("_");
        }
        if(paperStr.length() > 0){
            paperStr.deleteCharAt(paperStr.length() - 1);
        }


        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.TYPE, PayUtils.DOWNLOAD_TRUE_PAPER);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.CODE, code);
        paramsBean.put(PayParamsBean.EXAMS_REAL_PAPER_ID, paperStr.toString());
        return paramsBean.getParamsMap();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
