package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleCommentBean;
import com.dep.biguo.bean.LayerBean;
import com.dep.biguo.bean.OrganizationStudyBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.mvp.contract.OrganizationStudySearchContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationStudySearchPresenter extends BasePresenter<OrganizationStudySearchContract.Model, OrganizationStudySearchContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationStudySearchPresenter(OrganizationStudySearchContract.Model model, OrganizationStudySearchContract.View rootView) {
        super(model, rootView);
    }

    public void getSearch(String searchProfession, int province_id, int school_id, int layer_id, int institution_id, int form_id, int page) {
        mModel.getSearch(searchProfession, province_id, school_id, layer_id, institution_id, form_id, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationStudyBean.OrganizationItem>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationStudyBean.OrganizationItem>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData()) && page == 1){
                                mRootView.showEmptyView();
                                mRootView.getSearchSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView(false);
                                mRootView.getSearchSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getSearchFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getSearchFail();
                    }
                });
    }

    public void getProvince() {
        mModel.getProvince()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProvinceBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProvinceBean response) {
                        if (response.isSuccess()) {
                            mRootView.getProvince(response.getData());
                        }
                    }
                });
    }
    public void getSchool(int province_id, int institution_id, int form_id) {
        mModel.getSchool(province_id, institution_id, form_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<SchoolBean>(mErrorHandler) {
                    @Override
                    public void onNext(SchoolBean response) {
                        if (response.isSuccess()) {
                            mRootView.getSchool(response.getData());
                        }
                    }
                });
    }
    public void getLayer(int school_id, int institution_id, int form_id) {
        mModel.getLayer(school_id, institution_id, form_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<LayerBean>(mErrorHandler) {
                    @Override
                    public void onNext(LayerBean response) {
                        if (response.isSuccess()) {
                            mRootView.getLayer(response.getData());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
