package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationReservationRoomBean;
import com.dep.biguo.bean.OrganizationReservationRoomSuccessBean;
import com.dep.biguo.bean.OrganizationReservationRoomTimeBean;
import com.dep.biguo.mvp.contract.OrganizationReservationRoomContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationReservationRoomPresenter extends BasePresenter<OrganizationReservationRoomContract.Model, OrganizationReservationRoomContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationReservationRoomPresenter(OrganizationReservationRoomContract.Model model, OrganizationReservationRoomContract.View rootView) {
        super(model, rootView);
    }

    public void getReservationRoom(int activitiesId, String reserve_date) {
        mModel.getReservationRoom(activitiesId, reserve_date)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationReservationRoomBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationReservationRoomBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                if(AppUtil.isEmpty(s.getData().getReserve_dates())) {
                                    mRootView.showEmptyView();
                                }else {
                                    mRootView.showSuccessView();
                                    mRootView.getReservationRoomSuccess(s.getData());
                                }
                            }
                        }else {
                            mRootView.showEmptyView();
                            mRootView.getReservationRoomFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getReservationRoomFail();
                    }
                });

    }

    public void getTimePeriod(int activities_id, String region, String seat_number, int open_time_id){
        mModel.getTimePeriod(activities_id, region, seat_number,open_time_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationReservationRoomTimeBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationReservationRoomTimeBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.getTimeSuccess(new ArrayList<>());
                            }else {
                                mRootView.getTimeSuccess(s.getData());
                            }
                        }else {
                            mRootView.getTimeFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.getTimeFail();
                    }
                });

    }

    public void roomReservation(int activities_id, String region, String number, List<OrganizationReservationRoomTimeBean> timeList, int open_time_id) {
        StringBuilder builder = new StringBuilder();
        for (OrganizationReservationRoomTimeBean timeBean : timeList) {
            builder.append(builder.length() == 0 ? "" : ",")
                    .append(timeBean.getText());
        }
        if(builder.length() == 0){
            ToastUtils.show("请选择时间段");
            return;
        }
        //预约自习室传递1, 预约试听课传2
        mModel.roomReservation(activities_id, region, number, builder.toString(), open_time_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationReservationRoomSuccessBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationReservationRoomSuccessBean> s) {
                        if (s.isSuccess()) {
                            mRootView.reservationSuccess(region, number, s.getData());
                        }
                    }
                });

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
