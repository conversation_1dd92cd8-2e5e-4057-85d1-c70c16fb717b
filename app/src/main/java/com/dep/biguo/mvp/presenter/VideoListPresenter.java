package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.SkillChapterListBean;
import com.dep.biguo.mvp.contract.ViewListContract;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class VideoListPresenter extends BasePresenter<ViewListContract.Model, ViewListContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private SkillChapterListBean listBean;

    @Inject
    public VideoListPresenter(ViewListContract.Model model, ViewListContract.View rootView) {
        super(model, rootView);
    }

    public SkillChapterListBean getListBean() {
        return listBean;
    }

    /**获取视频的视频课程
     *
     */
    public void getVideoList(String courseCode, int skill_id, int product_id, int source_type) {
        mModel.getVideoList(courseCode, skill_id, product_id, source_type)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<SkillChapterListBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<SkillChapterListBean> response) {
                        if(mRootView == null) return;
                        //结束刷新
                        if (response.isSuccess()) {
                            if(response.getData() == null || AppUtil.isEmpty(response.getData().getList())){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                listBean = response.getData();
                                mRootView.getVideoListSuccess(response.getData());
                            }

                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**获取视频的播放地址
     *
     */
    public void getVideoPlayUrl(SkillChapterListBean.Video video) {
        mModel.getVideoPlayUrl(video.getSource_type(), video.getProduct_id(), video.getCourse_id(), video.getChapter_id(), video.getItem_id(), video.getCst_id(), video.getIs_free(), video.getVideo_aly_id())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if(mRootView == null) return;
                        if (response.isSuccess()) {
                            try {
                                String json = GsonUtils.toJson(response.getData());
                                //更新观看历史记录
                                listBean.setNew_view_id(String.format("%s_%s", video.getChapter_id(), video.getItem_id()));

                                if(TextUtils.isEmpty(video.getVideo_aly_id())) {
                                    Map<String, String> map = GsonUtils.fromJson(json, new TypeToken<Map<String, String>>(){}.getType());
                                    String url = map.get("video_url");
                                    if(TextUtils.isEmpty(url)){
                                        mRootView.showMessage("未获取到播放链接");
                                        return;
                                    }
                                    //获取播放链接成功
                                    mRootView.getVideoUrlSuccess(video, listBean.getSkill_id(), url);
                                }else {
                                    mRootView.getAliVideoUrlSuccess(video, json);
                                }
                            }catch (Exception e){
                                mRootView.showMessage("数据解析异常");
                            }
                        }
                    }

                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
