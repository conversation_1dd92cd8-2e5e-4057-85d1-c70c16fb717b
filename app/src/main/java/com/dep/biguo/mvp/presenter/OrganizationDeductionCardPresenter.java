package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationDeductionCardBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.OrganizationDeductionCardContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationDeductionCardPresenter extends BasePresenter<OrganizationDeductionCardContract.Model, OrganizationDeductionCardContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private PayResultListener mResultPayListener;//支付的监听接口

    @Inject
    public OrganizationDeductionCardPresenter(OrganizationDeductionCardContract.Model model, OrganizationDeductionCardContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void getDeductionCard(int institution_id){
        mModel.getDeductionCard(institution_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationDeductionCardBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationDeductionCardBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getDeductionCardSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getDeductionCardSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getDeductionCardFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getDeductionCardFail();
                    }
                });
    }

    /**支付
     * @param card_id   抵扣卡ID
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void payCard(int card_id, String payType) {
        mModel.paySingleOrder(getCreateOrderParams(card_id, payType))
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.showMessage("支付成功");
                            }
                        }
                    }
                });
    }

    /**构建创建订单需要的参数
     * @param card_id 抵扣卡ID
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     * @return
     */
    private Map<String,Object> getCreateOrderParams(int card_id, String payType){
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.TYPE, PayUtils.DEDUCTION_CARD);
        paramsBean.put(PayParamsBean.CARD_ID, card_id);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        return paramsBean.getParamsMap();
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
