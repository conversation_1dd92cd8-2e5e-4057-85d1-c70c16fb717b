package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.GraduateProxyProgressBean;
import com.dep.biguo.bean.OssSignatureBean;
import com.dep.biguo.mvp.contract.GraduateProxyProgressContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class GraduateProxyProgressPresenter extends BasePresenter<GraduateProxyProgressContract.Model, GraduateProxyProgressContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public GraduateProxyProgressPresenter(GraduateProxyProgressContract.Model model, GraduateProxyProgressContract.View rootView) {
        super(model, rootView);
    }

    public void getGraduateProxyProgress(int orderId) {
        mModel.getGraduateProxyProgress(orderId)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<GraduateProxyProgressBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<GraduateProxyProgressBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getGraduateProxyProgressSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getGraduateProxyProgressSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
