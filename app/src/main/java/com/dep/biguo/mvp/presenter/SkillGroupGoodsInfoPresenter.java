package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.GroupUserBean;
import com.dep.biguo.bean.RuleBean;
import com.dep.biguo.bean.SkillGroupGoodInfoBean;
import com.dep.biguo.mvp.contract.SkillGroupGoodsInfoContract;
import com.dep.biguo.mvp.ui.adapter.GroupCommentAdapter;
import com.dep.biguo.mvp.ui.adapter.GroupDoubtAdapter;
import com.dep.biguo.mvp.ui.adapter.GroupLabelAdapter;
import com.dep.biguo.mvp.ui.adapter.GroupListAdapter;
import com.dep.biguo.mvp.ui.adapter.GroupStructureAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class SkillGroupGoodsInfoPresenter extends BasePresenter<SkillGroupGoodsInfoContract.Model, SkillGroupGoodsInfoContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject GroupLabelAdapter mGroupLabelAdapter;//标签列表
    @Inject GroupStructureAdapter mStructureAdapter;//vip题型结构列表
    @Inject GroupListAdapter mGroupNewAdapter;//正在拼团列表
    @Inject GroupCommentAdapter mGroupCommentAdapter;//评论列表
    @Inject GroupDoubtAdapter mGroupDoubtAdapter;//常见问题列表

    private SkillGroupGoodInfoBean infoBean;

    private String goodsType;//必过：skill_video，职场提升：vocation_video
    private int product_id;
    private int skill_id;
    private int type;
    private boolean isBuy;

    private CountDownTimer mCountDownTimer;

    @Inject
    public SkillGroupGoodsInfoPresenter(SkillGroupGoodsInfoContract.Model model, SkillGroupGoodsInfoContract.View rootView) {
        super(model, rootView);
    }

    public void init(Bundle bundle){
        product_id = bundle.getInt(StartFinal.PRODUCT_ID);
        skill_id = bundle.getInt(StartFinal.SKILL_ID);
        goodsType = bundle.getString(StartFinal.GOODS_TYPE);
        type = bundle.getInt(StartFinal.TYPE);
        isBuy = bundle.getBoolean(StartFinal.IS_BUY);

        //获取初始数据
        getTabData(true);

        //初始化倒计时
        mCountDownTimer = new CountDownTimer(86400 * 1000, 100) {
            @Override
            public void onTick(long millisUntilFinished) {
                if(mGroupNewAdapter != null && mRootView != null) {
                    computerCountDown();
                }
            }

            @Override
            public void onFinish() {

            }
        };
    }

    public String getGoodsType() {
        return goodsType;
    }

    public String getCourseName() {
        return infoBean == null ? "" : infoBean.getName();
    }

    public int getProduct_id() {
        return product_id;
    }

    public String getGroupId(){
        if(infoBean == null) return "0";
        return infoBean.getGroup_id();
    }

    public boolean isBuy() {
        return isBuy;
    }

    public String getShareTitle(){
        if(infoBean != null){
            return infoBean.getShare_text();
        }
        return "";
    }
    public String getShareSubTitle(){
        if(infoBean != null){
            return infoBean.getShare_desc();
        }
        return "";
    }

    public String getGroupShareTitle(){
        if(infoBean != null){
            return infoBean.getGroup_share_text();
        }
        return "";
    }
    public String getGroupShareSubTitle(){
        if(infoBean != null){
            return infoBean.getGroup_share_desc();
        }
        return "";
    }

    public int getSkill_id() {
        return skill_id;
    }

    public SkillGroupGoodInfoBean getInfoBean() {
        return infoBean;
    }

    /**获取第一个tab的详情
     *
     */
    public void getTabData(boolean isShow) {
        mModel.getSkillTabData(product_id, skill_id,  type, goodsType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable ->{if(isShow) mRootView.showLoading();})
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() ->{if(isShow) mRootView.hideLoading();})
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<SkillGroupGoodInfoBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<SkillGroupGoodInfoBean> s) {
                        if (s.isSuccess()) {
                            infoBean = s.getData();
                            //当已购买时，需要更改presenter中的购买标记
                            isBuy = infoBean.getIs_pay() == 1;
                            //设置第一个Tab的数据
                            mRootView.setTabData(infoBean);
                            //设置标签
                            mGroupLabelAdapter.setNewData(infoBean.getLabel());
                            //设置正在拼团列表数据
                            mCountDownTimer.cancel();
                            mGroupNewAdapter.getData().clear();
                            if(infoBean.getConduct_group() != null) {
                                for (int i = 0; i < infoBean.getConduct_group().size(); i++) {
                                    GroupBean groupBean = infoBean.getConduct_group().get(i);
                                    groupBean.setSec(groupBean.getSec() * 1000);
                                    if(i < 2) {//mGroupNewAdapter最多显示两个团，因此小于2的时候才加入
                                        mGroupNewAdapter.getData().add(groupBean);
                                    }
                                }
                            }
                            mGroupNewAdapter.notifyDataSetChanged();
                            if(infoBean.getConduct_group() != null && infoBean.getConduct_group().size() > 0){
                                mCountDownTimer.start();
                            }
                            //设置评价列表数据
                            mGroupCommentAdapter.setNewData(infoBean.getComment());
                            //常见问题列表
                            mGroupDoubtAdapter.setNewData(infoBean.getQa());

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void getRule() {
        mModel.getRule("group_rule")
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RuleBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RuleBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getRuleSuccess(response.getData().getRules());
                        }
                    }
                });
    }

    /**返回所有拼团
     * @return
     */
    public List<GroupBean> getAllGroup(){
        return  infoBean == null || infoBean.getConduct_group() == null ? new ArrayList<>() : infoBean.getConduct_group();
    }

    /**寻找出自己发起的拼团
     * @return 没有则返回null
     */
    public GroupUserBean getJoiningGroup(){
        if(UserCache.getUserCache() == null || infoBean.getConduct_group() == null) return null;

        for(GroupBean groupBean : infoBean.getConduct_group()){
            for(GroupUserBean groupUserBean : groupBean.getUsers_info()){
                if(groupUserBean.getUsers_id() == UserCache.getUserCache().getUser_id() && groupUserBean.getIs_head() == 1){
                    return groupUserBean;
                }
            }
        }
        return null;
    }

    /**检查是否正在组团
     * @param data
     */
    public void isGrouping(GroupBean data){
        if(TextUtils.isEmpty(infoBean.getGroup_id())){//没有正在拼的团，则直接调起选择支付方式的弹窗
            mRootView.showJoinGroupDialog(data);

        }else {
            GroupUserBean joinUser = data.getUsers_info().get(0);
            if (joinUser.getUsers_id() == UserCache.getUserCache().getUser_id() && joinUser.getIs_head() == 1) {//如果点击的是自己发起的团，则弹出分享弹窗
                mRootView.showShareDialog(StartFinal.GROUP);
            } else {
                mRootView.showMessage("您已发起拼团~");
            }
        }
    }

    /**计算倒计时
     *
     */
    private void computerCountDown() {
        if (AppUtil.isEmpty(infoBean.getConduct_group())) return;

        for (GroupBean bean : infoBean.getConduct_group()) {
            bean.setSec(bean.getSec() <= 0 ? 0 : (bean.getSec() - 100));
        }

        mGroupNewAdapter.notifyTime();
        mRootView.refreshJoinDialog();
    }

    /**返回订单ID
     * @return
     */
    public int getOrderId(){
        if(infoBean == null) return 0;
        return infoBean.getOrder_id();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null) mCountDownTimer.cancel();
        LoadingDialog.hideLoadingDialog();
    }
}
