package com.dep.biguo.mvp.presenter;

import android.app.Application;

import androidx.annotation.NonNull;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.LayerBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.CounsellingZixuanReportContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class CounsellingZixuanReportPresenter extends BasePresenter<CounsellingZixuanReportContract.Model, CounsellingZixuanReportContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private List<DiscountBean> discountList;//优惠券列表

    private PayResultListener mResultPayListener;

    private int order_id;


    @Inject
    public CounsellingZixuanReportPresenter(CounsellingZixuanReportContract.Model model, CounsellingZixuanReportContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess(order_id);
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
            }
        };
    }

    public void getProvince() {
        mModel.getProvince()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProvinceBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProvinceBean response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showMessage("暂无省份");
                            }else {
                                mRootView.getProvinceSuccess(response.getData());
                            }
                        }
                    }
                });
    }


    public void getSchoolList(int province_id, int cert_project) {
        mModel.getSchoolList(province_id, cert_project)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<SchoolBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<SchoolBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showMessage("暂无学校");
                            }else {
                                mRootView.getSchoolListSuccess(response.getData());
                            }
                        }
                    }
                });
    }

    public void getLayerList(int cert_school, int cert_project) {
        mModel.getLayerList(cert_school, cert_project)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<LayerBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<LayerBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showMessage("暂无层次");
                            }else {
                                mRootView.getLayerListSuccess(response.getData());
                            }
                        }
                    }
                });
    }


    public void getProfession(int cert_school, int layer) {
        mModel.getProfession(cert_school, layer)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProfessionBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProfessionBean response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showMessage("暂无专业");
                            }else {
                                mRootView.getProfessionSuccess(response.getData());
                            }
                        }
                    }
                });
    }

    /**获取优惠券
     *
     */
    public void getDiscountCard(boolean isShowDiscountDialog){
        if(discountList != null) {
            if(isShowDiscountDialog) {
                if(!AppUtil.isEmpty(discountList)){
                    mRootView.showCouponDialog(discountList);
                    return;
                }
            }else {
                mRootView.showPayDialog(discountList);
                return;
            }
        }

        mModel.getDiscountCard(0, PayUtils.COUNSELLING_ZIXUAN, "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            if(isShowDiscountDialog) {
                                if(AppUtil.isEmpty(discountList)){
                                    mRootView.showMessage("暂无可用优惠券");
                                }else {
                                    mRootView.showCouponDialog(discountList);
                                }
                            }else {
                                mRootView.showPayDialog(discountList);
                            }
                        }
                    }

                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                    }
                });
    }


    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void pay(Map<String, Object> map, String payType, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(payType, selectedDiscountBean).getParamsMap();
        paramsMap.putAll(map);
        //根据是否拼团选择对应的接口
        mModel.paySingleOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            //创建订单成功，要清空优惠券列表，当下次再打开优惠券列表时，会自动请求服务器的数据
                            discountList = null;
                            mRootView.clearCoupon(null);

                            order_id = s.getData().getOrder_id();
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);
                            }
                        }
                    }
                });
    }

    private PayParamsBean getParams(String payType, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.TYPE, PayUtils.COUNSELLING_ZIXUAN);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());

        return paramsBean;
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
