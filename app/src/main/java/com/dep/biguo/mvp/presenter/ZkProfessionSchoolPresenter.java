package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.mvp.contract.ZkProfessionSchoolContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class ZkProfessionSchoolPresenter extends BasePresenter<ZkProfessionSchoolContract.Model, ZkProfessionSchoolContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ZkProfessionSchoolPresenter(ZkProfessionSchoolContract.Model model, ZkProfessionSchoolContract.View rootView) {
        super(model, rootView);
    }

    public void getSchoolList() {
        String cacheKey = UserCache.getAppType() +"-" + mRootView.getProvinceId();
        try {
            List<SchoolBean> schoolList = GsonUtils.fromJson(KVHelper.getString(cacheKey), new TypeToken<List<SchoolBean>>(){}.getType());
            if(!AppUtil.isEmpty(schoolList)) {
                mRootView.showSuccessView();
                mRootView.getSchoolListSuccess(schoolList);
            }
        }catch (Exception e){

        }

        mModel.getSchoolList(mRootView.getProvinceId())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if(TextUtils.isEmpty(KVHelper.getString(cacheKey))) {
                        mRootView.showLoadingView();
                    }
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<SchoolBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<SchoolBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getSchoolListSuccess(new ArrayList<>());

                            }else {
                                mRootView.showSuccessView();
                                mRootView.getSchoolListSuccess(response.getData());
                            }
                            KVHelper.putValue(cacheKey, GsonUtils.toJson(response.getData()));
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        if(TextUtils.isEmpty(KVHelper.getString(cacheKey))){
                            mRootView.showErrorView(t);
                        }
                    }
                });
    }

    /**获取专业
     * @param schoolName 学校名称
     */
    public void getProfession(String schoolName) {
        if (TextUtils.isEmpty(schoolName)) {
            mRootView.showMessage("请选择您所考试的学校");
            return;
        }

        String cacheKey = UserCache.getAppType() +"-" + mRootView.getProvinceId() +"-" +  mRootView.getLayer() +"-" +  schoolName;
        try {
            ProfessionBean professionBean = GsonUtils.fromJson(KVHelper.getString(cacheKey), ProfessionBean.class);
            if(!AppUtil.isEmpty(professionBean.getData())) {
                mRootView.showSuccessView(false);
                mRootView.getProfessionSuccess(professionBean.getData());
            }
        }catch (Exception e){

        }

        mModel.getProfession(mRootView.getProvinceId(), mRootView.getLayer(), schoolName)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if(TextUtils.isEmpty(KVHelper.getString(cacheKey))) {
                        mRootView.showLoadingView();
                    }
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProfessionBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProfessionBean response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showSuccessView(false);
                                mRootView.getProfessionSuccess(new ArrayList<>());

                            }else {
                                mRootView.showSuccessView(false);
                                mRootView.getProfessionSuccess(response.getData());
                            }
                            KVHelper.putValue(cacheKey, GsonUtils.toJson(response));
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        if(TextUtils.isEmpty(KVHelper.getString(cacheKey))){
                            mRootView.showErrorView(null);
                        }
                    }
                });
    }


    /**绑定省份、学校、专业
     *
     */
    public void bindProvinceProfession(CityBean.City city, SchoolBean schoolBean, ProfessionBean professionBean) {
        if (UserCache.getUserCache() == null) {
            mRootView.bindSuccess(schoolBean,professionBean);
            return;
        }

        mModel.bindProvinceProfession(city.getCity_id(), city.getProvince_id(), city.getCity_name(), professionBean.getId(), UserCache.getLongitude(), UserCache.getLatitude(), schoolBean.getId())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.bindSuccess(schoolBean,professionBean);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.killMyself();
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
