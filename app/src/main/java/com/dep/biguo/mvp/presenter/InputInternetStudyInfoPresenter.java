package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.InputInternetStudyInfoBean;
import com.dep.biguo.mvp.contract.InputInternetStudyInfoContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.CipherUtil;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class InputInternetStudyInfoPresenter extends BasePresenter<InputInternetStudyInfoContract.Model, InputInternetStudyInfoContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private CipherUtil cipherUtil;

    @Inject
    public InputInternetStudyInfoPresenter(InputInternetStudyInfoContract.Model model, InputInternetStudyInfoContract.View rootView) {
        super(model, rootView);
        cipherUtil = new CipherUtil();
    }

    /**获取个人信息
     *
     */
    public void getExamInfo(int orderId){
        mModel.getExamInfo(cipherUtil.getSendToServiceRsaPublicKey(), orderId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<InputInternetStudyInfoBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<InputInternetStudyInfoBean> s) {
                        mRootView.showSuccessView();
                        if (s.isSuccess()) {
                            InputInternetStudyInfoBean bean = s.getData();
                            cipherUtil.setServiceDecryptAesKey(bean.getKey());
                            String name = cipherUtil.decryptWithParams(bean.getReal_name());
                            String IDCard = cipherUtil.decryptWithParams(bean.getId_card());
                            String number = cipherUtil.decryptWithParams(bean.getExam_ticket());
                            String email = cipherUtil.decryptWithParams(bean.getEmail());
                            mRootView.getExamInfo(name, IDCard, number, email);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**提交个人信息
     *
     */
    public void commitExamInfo(String name, String IDCard, String number, String email, int order_id){
        Map<String, String> map = new HashMap<>();
        map.put("real_name", cipherUtil.encryptWithParams(name).replace("\n", ""));
        map.put("id_card", cipherUtil.encryptWithParams(IDCard).replace("\n", ""));
        map.put("exam_ticket", cipherUtil.encryptWithParams(number).replace("\n", ""));
        map.put("email", cipherUtil.encryptWithParams(email).replace("\n", ""));
        map.put("order_id", order_id+"");

        LogUtil.d("dddd", map);
        LogUtil.d("dddd", cipherUtil.getRandomEncryptAesKeyByRsa());

        mModel.commitExamInfo(map, cipherUtil.getRandomEncryptAesKeyByRsa())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.commitExamInfoSuccess();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
