package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.InviteToIntroduceHistoryBean;
import com.dep.biguo.bean.RewardCollectHistoryBean;
import com.dep.biguo.mvp.contract.InviteToIntroduceHistoryContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class InviteToIntroduceHistoryPresenter extends BasePresenter<InviteToIntroduceHistoryContract.Model, InviteToIntroduceHistoryContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public InviteToIntroduceHistoryPresenter(InviteToIntroduceHistoryContract.Model model, InviteToIntroduceHistoryContract.View rootView) {
        super(model, rootView);
    }
    public void getInviteToIntroduceHistory(int page){
        mModel.getInviteToIntroduceHistory(page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<InviteToIntroduceHistoryBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<InviteToIntroduceHistoryBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getInviteToIntroduceHistorySuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getInviteToIntroduceHistorySuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
