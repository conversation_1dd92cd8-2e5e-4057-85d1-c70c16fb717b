package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.OrganizationStudyReportContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class OrganizationStudyReportPresenter extends BasePresenter<OrganizationStudyReportContract.Model, OrganizationStudyReportContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private PayResultListener mResultPayListener;

    private int order_id;

    @Inject
    public OrganizationStudyReportPresenter(OrganizationStudyReportContract.Model model, OrganizationStudyReportContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess(order_id);
            }

            @Override
            public void onPayError() {
                order_id = 0;
            }

            @Override
            public void onPayCancel() {
                order_id = 0;

            }
        };
    }
    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void pay(Map<String, Object> map, String payType, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(payType, selectedDiscountBean).getParamsMap();
        paramsMap.putAll(map);
        //根据是否拼团选择对应的接口
        mModel.paySingleOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            order_id = s.getData().getOrder_id();
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);
                            }
                        }
                    }
                });
    }

    private PayParamsBean getParams(String payType, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.TYPE, PayUtils.INS_TUTORIAL_CLASS);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());
        return paramsBean;
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
