package com.dep.biguo.mvp.presenter;

import android.app.Application;
import com.dep.biguo.mvp.contract.SelectDateReservationContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import me.jessyan.rxerrorhandler.core.RxErrorHandler;

@FragmentScope
public class SelectDateReservationPresenter extends BasePresenter<SelectDateReservationContract.Model, SelectDateReservationContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public SelectDateReservationPresenter(SelectDateReservationContract.Model model, SelectDateReservationContract.View rootView) {
        super(model, rootView);
    }

    public void getDate(){
        List<String> list = new ArrayList<>();
        list.add("2023年10月17日");
        list.add("2023年10月18日");
        list.add("2023年10月19日");
        list.add("2023年10月20日");
        list.add("2023年10月21日");
        mRootView.getDateSuccess(list);
    }

    public void getTime(){
        List<String> list = new ArrayList<>();
        list.add("9:00-10:00");
        list.add("10:00-11:00");
        list.add("11:00-12:00");
        list.add("12:00-13:00");
        list.add("13:00-14:00");
        list.add("14:00-15:00");
        list.add("15:00-16:00");
        list.add("16:00-17:00");
        mRootView.getTimeSuccess(list);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
