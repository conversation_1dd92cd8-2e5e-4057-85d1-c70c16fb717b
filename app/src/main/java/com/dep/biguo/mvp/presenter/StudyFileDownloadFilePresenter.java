package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DownloadFileBean;
import com.dep.biguo.mvp.contract.StudyFileDownloadFileContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.mvp.ui.adapter.DownloadFileAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@FragmentScope
public class StudyFileDownloadFilePresenter extends BasePresenter<StudyFileDownloadFileContract.Model, StudyFileDownloadFileContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject DownloadFileAdapter fileAdapter;

    @Inject
    public StudyFileDownloadFilePresenter(StudyFileDownloadFileContract.Model model, StudyFileDownloadFileContract.View rootView) {
        super(model, rootView);
    }

    public void getCourseFileList(int product_id, int source_type, int page) {
        mModel.getCourseFileList(product_id, source_type, page)
                .subscribeOn(Schedulers.io()).retryWhen(new RetryWithDelay(0, 3))
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DownloadFileBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DownloadFileBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getCourseFileListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getCourseFileListSuccess(s.getData());
                                mRootView.initDownloadId(s.getData());
                            }

                        }else {
                            mRootView.showErrorView(new Throwable());
                            mRootView.getCourseFileListFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getCourseFileListFail();
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
