package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ClassTeacherEvaluateBean;
import com.dep.biguo.mvp.contract.ClassTeacherEvaluateContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ClassTeacherEvaluatePresenter extends BasePresenter<ClassTeacherEvaluateContract.Model, ClassTeacherEvaluateContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ClassTeacherEvaluatePresenter(ClassTeacherEvaluateContract.Model model, ClassTeacherEvaluateContract.View rootView) {
        super(model, rootView);
    }


    public void getClassTeacher(){
        mModel.getClassTeacher()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<ClassTeacherEvaluateBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<ClassTeacherEvaluateBean>> response) {
                        if(response.isSuccess()){
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                            }else {
                                /*response.getData().get(0).setIs_replace(0);
                                response.getData().get(0).setHasClassTeacher(0);*/
                                mRootView.showSuccessView();
                                mRootView.getClassTeacherSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void accessClassTeacher(int teacher_id, int product_id, String content, List<String> filepath, int is_replace, int score_grade){
        LogUtil.d("dddd", filepath);
        mModel.accessClassTeacher(teacher_id, product_id, content, filepath, is_replace, score_grade)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if(response.isSuccess()) {
                            mRootView.accessClassTeacherSuccess();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
