package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.AliyunAuthBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VipCourseClassDetailBean;
import com.dep.biguo.live.LiveHelper;
import com.dep.biguo.mvp.contract.VipCourseClassDetailContract;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.ActivityEvent;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


/**
 * ================================================
 * Description:
 * <p>
 * Created by MVPArmsTemplate on 06/23/2020 17:19
 * <a href="mailto:<EMAIL>">Contact me</a>
 * <a href="https://github.com/JessYanCoding">Follow me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms">Star me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms/wiki">See me</a>
 * <a href="https://github.com/JessYanCoding/MVPArmsTemplate">模版请保持更新</a>
 * ================================================
 */
@ActivityScope
public class VipCourseClassDetailPresenter extends BasePresenter<VipCourseClassDetailContract.Model, VipCourseClassDetailContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    public VipCourseClassDetailPresenter(VipCourseClassDetailContract.Model model, VipCourseClassDetailContract.View rootView) {
        super(model, rootView);
    }


    public void getVipCourseClassDetail(int classroom_id, String code) {
        mModel.getVipCourseClassDetail(classroom_id, code)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<VipCourseClassDetailBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<VipCourseClassDetailBean> s) {
                        if (s.isSuccess()) {
                            mRootView.setData(s.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void getLiveAppointment(int course_arrange_id) {
        mModel.getLiveAppointment(course_arrange_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void getLiveRecord(String playback_id) {
        mModel.getLiveRecord(playback_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }


    public void getPlayerAuth(boolean showDialog, String video_aly_id, final VipCourseClassDetailBean.VideoBean videoBean) {
        mModel.getPlayAuth(video_aly_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (showDialog)
                        mRootView.showLoadingDialog();
                    else
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<AliyunAuthBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<AliyunAuthBean> response) {
                        mRootView.hideLoading();
                        mRootView.hideLoadingDialog();
                        if (response.isSuccess()) {
                            LiveHelper.openAliYunReplayRoom(mRootView.getActivity(), response.getData(),videoBean.getName(), videoBean.getCourses_name(), videoBean.getTeacher_name(), UserCache.getUserCache().getUser_id(), mRootView.getCode());
                        } else {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
