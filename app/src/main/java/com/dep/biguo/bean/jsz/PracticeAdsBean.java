package com.dep.biguo.bean.jsz;

import com.dep.biguo.bean.AdBean;

import java.util.List;

public class PracticeAdsBean {
    private List<AdBean> top;
    private List<AdBean> comments;
    private List<AdBean> circle;
    private List<AdBean> real_paper;
    private List<AdBean> chapter;
    private List<AdBean> vip;
    private List<AdBean> high_frequency;
    private List<AdBean> prac;
    private List<AdBean> kf;
    private List<Slider> slider;
    private AdBean banner;
    private int ai_supported;
    private String ai_domain;

    public List<AdBean> getTop() {
        return top;
    }

    public void setTop(List<AdBean> top) {
        this.top = top;
    }

    public List<AdBean> getComments() {
        return comments;
    }

    public void setComments(List<AdBean> comments) {
        this.comments = comments;
    }

    public List<AdBean> getCircle() {
        return circle;
    }

    public void setCircle(List<AdBean> circle) {
        this.circle = circle;
    }

    public List<AdBean> getReal_paper() {
        return real_paper;
    }

    public void setReal_paper(List<AdBean> real_paper) {
        this.real_paper = real_paper;
    }

    public List<AdBean> getChapter() {
        return chapter;
    }

    public void setChapter(List<AdBean> chapter) {
        this.chapter = chapter;
    }

    public List<AdBean> getVip() {
        return vip;
    }

    public void setVip(List<AdBean> vip) {
        this.vip = vip;
    }

    public List<AdBean> getHigh_frequency() {
        return high_frequency;
    }

    public void setHigh_frequency(List<AdBean> high_frequency) {
        this.high_frequency = high_frequency;
    }

    public List<AdBean> getPrac() {
        return prac;
    }

    public void setPrac(List<AdBean> prac) {
        this.prac = prac;
    }

    public List<AdBean> getKf() {
        return kf;
    }

    public void setKf(List<AdBean> kf) {
        this.kf = kf;
    }

    public int getAi_supported() {
        return ai_supported;
    }

    public void setAi_supported(int ai_supported) {
        this.ai_supported = ai_supported;
    }

    public String getAi_domain() {
        return ai_domain;
    }

    public void setAi_domain(String ai_domain) {
        this.ai_domain = ai_domain;
    }

    public List<Slider> getSlider() {
        return slider;
    }

    public void setSlider(List<Slider> slider) {
        this.slider = slider;
    }

    public AdBean getBanner() {
        return banner;
    }

    public void setBanner(AdBean banner) {
        this.banner = banner;
    }

    public static class Slider{
        private String title;
        private String icon_url;
        private String subtitle;
        private String type;
        private ExtendBean extend;
        private int is_enable;
        private int is_buy;
        private int need_login;
        private String background_color;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getIcon_url() {
            return icon_url;
        }

        public void setIcon_url(String icon_url) {
            this.icon_url = icon_url;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public ExtendBean getExtend() {
            return extend;
        }

        public void setExtend(ExtendBean extend) {
            this.extend = extend;
        }

        public int getIs_enable() {
            return is_enable;
        }

        public void setIs_enable(int is_enable) {
            this.is_enable = is_enable;
        }

        public int getIs_buy() {
            return is_buy;
        }

        public void setIs_buy(int is_buy) {
            this.is_buy = is_buy;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }

        public String getBackground_color() {
            return background_color;
        }

        public void setBackground_color(String background_color) {
            this.background_color = background_color;
        }

        public static class ExtendBean {
            private int product_id;
            private String code;
            private int source_type;
            private int is_formulate_study_plan;
            private int is_plan_in_profession;
            private int is_online_assistance;

            public int getProduct_id() {
                return product_id;
            }

            public void setProduct_id(int product_id) {
                this.product_id = product_id;
            }

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public int getSource_type() {
                return source_type;
            }

            public void setSource_type(int source_type) {
                this.source_type = source_type;
            }

            public int getIs_formulate_study_plan() {
                return is_formulate_study_plan;
            }

            public void setIs_formulate_study_plan(int is_formulate_study_plan) {
                this.is_formulate_study_plan = is_formulate_study_plan;
            }

            public int getIs_plan_in_profession() {
                return is_plan_in_profession;
            }

            public void setIs_plan_in_profession(int is_plan_in_profession) {
                this.is_plan_in_profession = is_plan_in_profession;
            }

            public int getIs_online_assistance() {
                return is_online_assistance;
            }

            public void setIs_online_assistance(int is_online_assistance) {
                this.is_online_assistance = is_online_assistance;
            }
        }
    }
}
